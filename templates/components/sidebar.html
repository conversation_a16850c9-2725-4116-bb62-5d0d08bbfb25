<!-- 侧边栏样式 -->
<style>
    .sidebar {
        width: 250px;
        min-width: 250px;
        height: 100vh;
        position: fixed;
        top: 0;
        left: 0;
        background-color: #f8f9fa;
        padding: 20px 0;
        box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        z-index: 100;
        overflow-y: auto;
        transition: transform 0.3s ease;
    }
    
    .sidebar.collapsed {
        transform: translateX(-100%);
    }
    
    .sidebar-content {
        padding: 0 15px;
    }
    
    .sidebar-header {
        padding: 0 15px 15px;
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 15px;
    }
    
    .sidebar-category-item {
        display: block;
        padding: 10px 15px;
        margin-bottom: 5px;
        border-radius: 5px;
        color: #495057;
        text-decoration: none;
        transition: all 0.2s;
    }
    
    .sidebar-category-item:hover {
        background-color: #e9ecef;
    }
    
    .sidebar-category-item.active {
        background-color: #007bff;
        color: white;
    }
    
    .sidebar-category-item .tag-count {
        float: right;
        background-color: rgba(0,0,0,0.1);
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 0.8em;
    }
    
    .sidebar-category-item.active .tag-count {
        background-color: rgba(255,255,255,0.3);
    }
    
    /* 主内容区域样式 */
    .main-content {
        margin-left: 250px;
        transition: margin-left 0.3s ease;
    }
    
    .main-content.expanded {
        margin-left: 0;
    }
    
    /* 汉堡菜单按钮样式 */
    .menu-toggle {
        display: none;
        position: fixed;
        top: 15px;
        left: 15px;
        z-index: 101;
        background-color: rgba(0, 123, 255, 0.5);
        color: white;
        border: none;
        border-radius: 4px;
        padding: 5px;
        cursor: pointer;
        font-size: 16px;
        line-height: 1;
        text-align: center;
        vertical-align: middle;
        min-width: 30px;
        min-height: 30px;
        backdrop-filter: blur(4px);
        -webkit-backdrop-filter: blur(4px);
        transition: background-color 0.2s ease;
    }
    
    .menu-toggle:hover {
        background-color: rgba(0, 123, 255, 0.6);
    }
    
    .overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        z-index: 99;
    }
    
    /* 移动端适配 */
    @media (max-width: 768px) {
        .sidebar {
            transform: translateX(-100%);
        }
        
        .sidebar.active {
            transform: translateX(0);
        }
        
        .main-content {
            margin-left: 0;
        }
        
        .menu-toggle {
            display: block;
        }
    }
</style>

<!-- 汉堡菜单按钮 -->
<button class="menu-toggle" id="menuToggle">☰</button>

<!-- 遮罩层 -->
<div class="overlay" id="overlay"></div>

<!-- 侧边栏 -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <h3 class="m-0">帖子分类</h3>
    </div>
    <div class="sidebar-content">
        <!-- 搜索表单 -->
        <form action="{{ url_for('search') }}" method="get" class="mb-4">
            <div class="relative">
                <input type="text" name="q" placeholder="搜索帖子..." 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                       required>
                <button type="submit" class="absolute right-2 top-2 text-gray-400 hover:text-primary-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </button>
            </div>
        </form>

        <a href="{{ url_for('index') }}" class="sidebar-category-item {% if not selected_category %}active{% endif %}">全部 <span class="tag-count">{{ categories | sum(attribute='count') }}</span></a>
        {% for cat in categories %}
        <a href="{{ url_for('index', category=cat.category) }}" class="sidebar-category-item {% if selected_category == cat.category %}active{% endif %}">
            {{ cat.category }}
            <span class="tag-count">{{ cat.count }}</span>
        </a>
        {% endfor %}
        <a href="{% if selected_category %}{{ url_for('new_post', category=selected_category) }}{% else %}{{ url_for('new_post') }}{% endif %}" class="btn-success block text-center mt-3">发布新帖</a>
    </div>
</div>

<!-- 侧边栏JavaScript功能 -->
<script>
    // 侧边栏折叠功能
    document.addEventListener('DOMContentLoaded', function() {
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        const mainContent = document.getElementById('mainContent');
        
        // 检查屏幕宽度，设置初始状态
        function checkScreenSize() {
            if (window.innerWidth <= 768) {
                sidebar.classList.remove('active');
                mainContent.classList.add('expanded');
            } else {
                sidebar.classList.add('active');
                mainContent.classList.remove('expanded');
            }
        }
        
        // 初始化
        checkScreenSize();
        
        // 监听窗口大小变化
        window.addEventListener('resize', checkScreenSize);
        
        // 汉堡菜单点击事件
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
            overlay.style.display = 'block';
        });
        
        // 遮罩层点击事件
        overlay.addEventListener('click', function() {
            sidebar.classList.remove('active');
            overlay.style.display = 'none';
        });
        
        // 点击侧边栏链接后关闭侧边栏（移动端）
        const sidebarLinks = document.querySelectorAll('.sidebar-category-item');
        sidebarLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                    overlay.style.display = 'none';
                }
            });
        });
    });
</script>
