<!-- 帖子表单共享组件 -->
<style>
    /* 确保body和主要容器有足够高度但不超出视窗 */
    body {
        height: 100vh; /* 使用固定高度 */
        margin: 0;
        display: flex;
        flex-direction: column;
        overflow: hidden; /* 防止body产生滚动条 */
    }
    .max-w-4xl.mx-auto {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
        width: 100%;
        max-width: 56rem;
        overflow-y: auto; /* 允许内容区域滚动 */
    }
    
    /* 内容区域扩展优化 */
    .form-container {
        display: flex;
        flex-direction: column;
        flex: 1;
    }
    form {
        display: flex;
        flex-direction: column;
        flex: 1;
    }
    .content-group {
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    .form-textarea {
        flex: 1;
        resize: vertical;
        min-height: 300px;
    }

    /* 字数统计样式 */
    .character-count {
        font-size: 0.75rem;
        color: #6b7280;
        font-weight: 400;
    }
    .character-count.highlight {
        color: #3b82f6;
        font-weight: 500;
    }
    
    /* 选中统计样式 */
    .selection-count {
        font-size: 0.75rem;
        color: #8b5cf6;
        font-weight: 400;
        margin-right: 0.5rem;
        padding-right: 0.5rem;
        border-right: 1px solid #e5e7eb;
    }
    .selection-count.highlight {
        color: #7c3aed;
        font-weight: 500;
    }
    .selection-count.empty {
        color: #9ca3af;
    }
    
    /* 字段折叠控制样式 */
    .fields-toggle {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
        padding: 8px 12px;
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        user-select: none;
    }
    .fields-toggle:hover {
        background: #f3f4f6;
        border-color: #d1d5db;
    }
    .fields-toggle-icon {
        width: 16px;
        height: 16px;
        color: #6b7280;
        transition: transform 0.2s ease;
    }
    .fields-toggle.collapsed .fields-toggle-icon {
        transform: rotate(-90deg);
    }
    .fields-toggle-text {
        font-size: 14px;
        color: #374151;
        font-weight: 500;
    }
    .fields-toggle-hint {
        font-size: 12px;
        color: #6b7280;
        margin-left: auto;
    }
    
    /* 字段收起/展开动画 */
    .form-fields-container {
        overflow: hidden;
        transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
                   opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        margin-bottom: 16px; /* 固定外边距，避免跳跃 */
    }
    .form-fields-container.collapsed {
        max-height: 0;
        opacity: 0;
        /* 移除 margin-bottom 和 padding 的动画变化 */
    }
    .form-fields-container.expanded {
        max-height: 500px; /* 设置具体值而非 none，确保动画平滑 */
        opacity: 1;
    }
    
    /* 隐藏字段时文本域自动扩展 */
    .form-container.fields-hidden .content-group {
        flex: 1;
    }
    .form-container.fields-hidden .form-textarea {
        /* 使用 flex 自适应高度，无需固定计算 */
        flex: 1;
        min-height: 200px; /* 设置最小高度避免过小 */
        resize: vertical;
        transition: min-height 0.3s ease;
    }
    
    /* 展开字段时保持原有高度 */
    .form-container.fields-visible .form-textarea {
        flex: 1;
        min-height: 300px;
        resize: vertical;
        transition: min-height 0.3s ease;
    }
    
    /* 防止内容区域在动画过程中产生布局偏移 */
    .content-group {
        transition: flex 0.3s ease;
    }

    /* 行号编辑器样式 */
    .editor-container {
        position: relative;
        display: flex;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        overflow: hidden;
        background: #ffffff;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
        font-size: 14px;
        line-height: 1.5;
    }

    .line-numbers {
        background: #f8fafc;
        border-right: 1px solid #e5e7eb;
        color: #9ca3af;
        font-size: 13px;
        line-height: 1.5;
        padding: 12px 8px 12px 12px;
        text-align: right;
        user-select: none;
        min-width: 50px;
        white-space: pre;
        overflow: hidden;
        position: relative;
        z-index: 1;
    }

    .line-numbers::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        width: 1px;
        background: #e5e7eb;
    }

    .editor-wrapper {
        flex: 1;
        position: relative;
        display: flex;
        flex-direction: column;
    }

    .form-textarea-with-lines {
        border: none;
        outline: none;
        resize: none;
        padding: 12px 16px;
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
        background: transparent;
        width: 100%;
        flex: 1;
        min-height: 300px;
        overflow-y: auto;
        white-space: pre-wrap;
        word-wrap: break-word;
    }

    .form-textarea-with-lines:focus {
        outline: none;
        box-shadow: none;
    }

    .editor-container:focus-within {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* 隐藏字段时编辑器自动扩展 */
    .form-container.fields-hidden .form-textarea-with-lines {
        min-height: 200px;
    }

    /* 展开字段时保持原有高度 */
    .form-container.fields-visible .form-textarea-with-lines {
        min-height: 300px;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .line-numbers {
            min-width: 40px;
            padding: 12px 6px 12px 8px;
            font-size: 12px;
        }

        .form-textarea-with-lines {
            padding: 12px;
            font-size: 13px;
        }

        .editor-container {
            font-size: 13px;
        }
    }
</style>

<script>
    // 帖子草稿存储相关功能
    function getPostDraftKey(postId = 'new') {
        return `haobbs_draft_post_${postId}`;
    }

    function savePostDraft(postId, formData) {
        if (!formData.title.trim() && !formData.content.trim() && !formData.category.trim()) {
            // 所有字段都为空，删除草稿
            removePostDraft(postId);
            return;
        }

        const draftData = {
            title: formData.title,
            content: formData.content,
            category: formData.category,
            created_at: formData.created_at,
            timestamp: Date.now(),
            expires: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7天后过期
        };

        localStorage.setItem(getPostDraftKey(postId), JSON.stringify(draftData));
    }

    function loadPostDraft(postId) {
        const key = getPostDraftKey(postId);
        const draftJson = localStorage.getItem(key);
        
        if (!draftJson) return null;

        try {
            const draftData = JSON.parse(draftJson);
            
            // 检查是否过期
            if (Date.now() > draftData.expires) {
                localStorage.removeItem(key);
                return null;
            }

            return draftData;
        } catch (e) {
            console.error('Failed to parse post draft data:', e);
            localStorage.removeItem(key);
            return null;
        }
    }

    function removePostDraft(postId) {
        localStorage.removeItem(getPostDraftKey(postId));
    }

    function hasUnsavedChanges() {
        const titleInput = document.getElementById('title');
        const contentInput = document.getElementById('content');
        const categoryInput = document.getElementById('category');
        const createdAtInput = document.getElementById('created_at');
        
        if (!titleInput || !contentInput || !categoryInput) return false;
        
        // 获取原始值（用于编辑模式）
        const originalTitle = titleInput.getAttribute('data-original-value') || '';
        const originalContent = contentInput.getAttribute('data-original-value') || '';
        const originalCategory = categoryInput.getAttribute('data-original-value') || '';
        const originalCreatedAt = createdAtInput ? createdAtInput.getAttribute('data-original-value') || '' : '';
        
        // 检查是否有变更
        return titleInput.value.trim() !== originalTitle.trim() ||
               contentInput.value.trim() !== originalContent.trim() ||
               categoryInput.value.trim() !== originalCategory.trim() ||
               (createdAtInput && createdAtInput.value.trim() !== originalCreatedAt.trim());
    }

    // 智能返回功能
    function smartGoBack() {
        // 检查是否有未保存的内容
        if (hasUnsavedChanges()) {
            // 获取当前页面的postId
            const currentPostId = window.location.pathname.includes('/edit') ? 
                window.location.pathname.split('/')[2] : 'new';
            
            const confirmMessage = currentPostId === 'new' 
                ? '💾 内容已自动保存为草稿\n\n确定要返回吗？下次访问时可继续编辑。'
                : '💾 编辑内容已自动保存为草稿\n\n确定要返回吗？下次编辑时可继续修改。';
                
            if (!confirm(confirmMessage)) {
                return; // 用户选择继续编辑
            }
        }
        
        // 获取预设的返回 URL，如果没有则默认返回首页
        const cancelUrl = '{{ cancel_url if cancel_url else url_for("index") }}';
        
        console.log('🔄 开始执行返回操作');
        console.log('  - 当前URL:', window.location.href);
        console.log('  - 来源页面:', document.referrer);
        console.log('  - 预设返回URL:', cancelUrl);
        
        // 简化返回逻辑，直接使用预设的cancelUrl确保可靠性
        try {
            console.log('✅ 使用预设返回URL跳转:', cancelUrl);
            window.location.href = cancelUrl;
        } catch (error) {
            console.error('❌ 返回操作出错:', error);
            // 备选方案：直接跳转到首页
            console.log('🏠 使用备选方案：跳转到首页');
            window.location.href = '{{ url_for("index") }}';
        }
    }

    // 添加一些快捷键支持
    document.addEventListener('DOMContentLoaded', function() {
        // 显示对话框功能（符合用户偏好）
        function showSaveDialog(message) {
            // 创建对话框元素
            const dialog = document.createElement('div');
            dialog.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
                z-index: 1000;
                padding: 24px;
                min-width: 320px;
                text-align: center;
            `;
            
            // 对话框内容（添加成功图标）
            dialog.innerHTML = `
                <div style="margin-bottom: 16px; font-size: 16px; color: #374151; display: flex; align-items: center; justify-content: center; gap: 8px;">
                    <svg style="width: 20px; height: 20px; color: #10b981;" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <span>${message}</span>
                </div>
                <button onclick="this.parentElement.remove()" style="
                    background: #007bff;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: background-color 0.2s;
                " onmouseover="this.style.backgroundColor='#0056b3'" onmouseout="this.style.backgroundColor='#007bff'">我知道了</button>
            `;
            
            document.body.appendChild(dialog);
            
            // 3秒后自动关闭
            setTimeout(() => {
                if (dialog.parentElement) {
                    dialog.remove();
                }
            }, 3000);
        }
        
        // 行号编辑器功能
        function initLineNumberEditor() {
            const textarea = document.getElementById('content');
            const lineNumbers = document.getElementById('line-numbers');

            if (!textarea || !lineNumbers) return;

            // 更新行号显示
            function updateLineNumbers() {
                const lines = textarea.value.split('\n');
                const lineCount = lines.length;

                // 生成行号文本
                let lineNumberText = '';
                for (let i = 1; i <= lineCount; i++) {
                    lineNumberText += i + '\n';
                }

                // 移除最后一个换行符
                lineNumbers.textContent = lineNumberText.slice(0, -1);
            }

            // 同步滚动
            function syncScroll() {
                lineNumbers.scrollTop = textarea.scrollTop;
            }

            // 同步高度
            function syncHeight() {
                // 确保行号容器与文本区域高度一致
                const textareaHeight = textarea.scrollHeight;
                const textareaStyle = window.getComputedStyle(textarea);
                const paddingTop = parseInt(textareaStyle.paddingTop);
                const paddingBottom = parseInt(textareaStyle.paddingBottom);

                // 设置行号容器的最小高度
                lineNumbers.style.minHeight = (textareaHeight - paddingTop - paddingBottom) + 'px';
            }

            // 初始化行号
            updateLineNumbers();
            syncHeight();

            // 监听文本变化
            textarea.addEventListener('input', function() {
                updateLineNumbers();
                // 使用 setTimeout 确保 DOM 更新后再同步高度
                setTimeout(syncHeight, 0);
            });

            // 监听滚动事件
            textarea.addEventListener('scroll', syncScroll);

            // 监听窗口大小变化
            window.addEventListener('resize', function() {
                setTimeout(syncHeight, 100);
            });

            // 监听文本区域大小变化（当用户拖拽调整大小时）
            if (window.ResizeObserver) {
                const resizeObserver = new ResizeObserver(function() {
                    syncHeight();
                });
                resizeObserver.observe(textarea);
            }

            // 处理粘贴事件
            textarea.addEventListener('paste', function() {
                // 延迟更新，确保粘贴内容已经插入
                setTimeout(function() {
                    updateLineNumbers();
                    syncHeight();
                }, 10);
            });

            // 处理键盘事件（特别是 Enter 键）
            textarea.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    // 延迟更新行号，确保换行已经生效
                    setTimeout(function() {
                        updateLineNumbers();
                        syncHeight();
                    }, 0);
                }
            });
        }

        // 字段折叠切换功能（简化版本，使用 flex 自适应高度）
        function initFieldsToggle() {
            const toggle = document.getElementById('fields-toggle');
            const container = document.getElementById('form-fields-container');
            const formContainer = document.querySelector('.form-container');
            const icon = document.getElementById('toggle-icon');
            const text = document.getElementById('toggle-text');
            
            if (!toggle || !container || !icon || !text || !formContainer) return;
            
            // 从 localStorage 读取状态，默认展开
            const isCollapsed = localStorage.getItem('haobbs_fields_collapsed') === 'true';
            
            function updateToggleState(collapsed) {
                // 使用 requestAnimationFrame 确保 DOM 更新的平滑性
                requestAnimationFrame(() => {
                    if (collapsed) {
                        container.classList.remove('expanded');
                        container.classList.add('collapsed');
                        toggle.classList.add('collapsed');
                        formContainer.classList.remove('fields-visible');
                        formContainer.classList.add('fields-hidden');
                        text.textContent = '显示表单字段';
                    } else {
                        container.classList.remove('collapsed');
                        container.classList.add('expanded');
                        toggle.classList.remove('collapsed');
                        formContainer.classList.remove('fields-hidden');
                        formContainer.classList.add('fields-visible');
                        text.textContent = '隐藏表单字段';
                    }
                    
                    // 保存状态到 localStorage
                    localStorage.setItem('haobbs_fields_collapsed', collapsed.toString());
                });
            }
            
            // 初始化状态 - 延迟执行避免页面加载时的视觉跳跃
            setTimeout(() => {
                updateToggleState(isCollapsed);
            }, 50);
            
            // 点击事件 - 添加防抖处理
            let isToggling = false;
            toggle.addEventListener('click', function() {
                if (isToggling) return; // 防止快速重复点击
                
                isToggling = true;
                const currentCollapsed = container.classList.contains('collapsed');
                const newCollapsed = !currentCollapsed;
                
                updateToggleState(newCollapsed);
                
                // 动画完成后重置防抖标志
                setTimeout(() => {
                    isToggling = false;
                }, 300); // 与 CSS 动画时长保持一致
            });
        }
        const textarea = document.getElementById('content');
        const charCount = document.getElementById('character-count');
        const selectionCount = document.getElementById('selection-count');
        const titleInput = document.getElementById('title');
        const categoryInput = document.getElementById('category');
        const createdAtInput = document.getElementById('created_at');
        
        // 获取帖子ID（编辑模式时从URL获取，新建模式时为'new'）
        const postId = window.location.pathname.includes('/edit') ? 
            window.location.pathname.split('/')[2] : 'new';

        // 字符统计函数（只统计中文、英文和数字）
        function countAllCharacters(text) {
            // 使用正则表达式匹配中文、英文和数字字符
            const validChars = text.match(/[\u4e00-\u9fa5a-zA-Z0-9]/g);
            return validChars ? validChars.length : 0;
        }

        // 更新字数统计
        function updateCharacterCount() {
            const text = textarea.value;
            const count = countAllCharacters(text);
            charCount.textContent = count + ' 字';
        }

        // 更新选中统计
        function updateSelectionCount() {
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            
            if (start === end) {
                // 没有选中文本
                selectionCount.textContent = '选中: 0 字';
                selectionCount.classList.add('empty');
                selectionCount.classList.remove('highlight');
            } else {
                // 有选中文本，统计其中的字符
                const selectedText = textarea.value.substring(start, end);
                const count = countAllCharacters(selectedText);
                selectionCount.textContent = '选中: ' + count + ' 字';
                selectionCount.classList.remove('empty');
                
                // 如果选中文本包含字符，高亮显示
                if (count > 0) {
                    selectionCount.classList.add('highlight');
                } else {
                    selectionCount.classList.remove('highlight');
                }
            }
        }

        if (textarea && charCount && selectionCount) {
            // 存储原始值用于变更检测
            if (titleInput) titleInput.setAttribute('data-original-value', titleInput.value);
            if (categoryInput) categoryInput.setAttribute('data-original-value', categoryInput.value);
            if (textarea) textarea.setAttribute('data-original-value', textarea.value);
            if (createdAtInput) createdAtInput.setAttribute('data-original-value', createdAtInput.value);
            
            // 尝试加载草稿
            const draft = loadPostDraft(postId);
            if (draft) {
                // 新建模式：直接加载草稿内容（如果字段为空）
                if (postId === 'new') {
                    if (titleInput && !titleInput.value.trim()) titleInput.value = draft.title || '';
                    
                    // 智能分类处理逻辑：
                    // 1. 如果草稿中的分类为空，则使用当前选中的分类（预填充值）
                    // 2. 如果草稿中的分类不为空，则使用草稿中的分类
                    if (categoryInput) {
                        const currentCategory = categoryInput.value.trim(); // 当前预填充的分类
                        const draftCategory = draft.category ? draft.category.trim() : ''; // 草稿中的分类
                        
                        if (draftCategory) {
                            // 草稿中有分类，使用草稿中的分类
                            categoryInput.value = draftCategory;
                        } else if (currentCategory) {
                            // 草稿中没有分类，保持当前预填充的分类
                            // categoryInput.value 保持不变
                        } else {
                            // 都没有分类，设置为空
                            categoryInput.value = '';
                        }
                    }
                    
                    if (textarea && !textarea.value.trim()) textarea.value = draft.content || '';
                    if (createdAtInput && !createdAtInput.value.trim()) createdAtInput.value = draft.created_at || '';
                } else {
                    // 编辑模式：始终加载草稿内容（覆盖数据库内容）
                    if (titleInput) titleInput.value = draft.title || titleInput.value;
                    if (categoryInput) categoryInput.value = draft.category || categoryInput.value;
                    if (textarea) textarea.value = draft.content || textarea.value;
                    if (createdAtInput) createdAtInput.value = draft.created_at || createdAtInput.value;
                }
            }
            
            // 初始化字数统计
            updateCharacterCount();
            updateSelectionCount();
            
            // 检查是否有保存成功的标识
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('saved') === '1') {
                // 显示保存成功对话框
                setTimeout(() => {
                    showSaveDialog('保存成功！可继续编辑或点击发布。');
                    // 移除URL中的saved参数，避免重复显示
                    const newUrl = new URL(window.location);
                    newUrl.searchParams.delete('saved');
                    window.history.replaceState({}, '', newUrl);
                }, 500); // 稍微延迟显示，确保页面完全加载
            }

            // 自动保存草稿函数
            function autoSavePostDraft() {
                const formData = {
                    title: titleInput ? titleInput.value : '',
                    content: textarea ? textarea.value : '',
                    category: categoryInput ? categoryInput.value : '',
                    created_at: createdAtInput ? createdAtInput.value : ''
                };
                savePostDraft(postId, formData);
            }
            
            // 监听输入事件实时更新字数并保存草稿
            textarea.addEventListener('input', function() {
                updateCharacterCount();
                autoSavePostDraft();
            });
            textarea.addEventListener('propertychange', updateCharacterCount); // 兼容IE
            
            // 监听其他字段输入事件
            if (titleInput) {
                titleInput.addEventListener('input', autoSavePostDraft);
            }
            if (categoryInput) {
                categoryInput.addEventListener('input', autoSavePostDraft);
            }
            if (createdAtInput) {
                createdAtInput.addEventListener('input', autoSavePostDraft);
            }

            // 监听选中相关事件
            textarea.addEventListener('select', updateSelectionCount);
            textarea.addEventListener('selectionchange', updateSelectionCount);
            textarea.addEventListener('mouseup', updateSelectionCount);
            textarea.addEventListener('keyup', updateSelectionCount);

            textarea.addEventListener('keydown', function(e) {
                // Ctrl+B 加粗
                if (e.ctrlKey && e.key === 'b') {
                    e.preventDefault();
                    insertMarkdown('**', '**');
                }
                // Ctrl+I 斜体
                if (e.ctrlKey && e.key === 'i') {
                    e.preventDefault();
                    insertMarkdown('*', '*');
                }
                // Tab键插入4个空格
                if (e.key === 'Tab') {
                    e.preventDefault();
                    insertText('    ');
                }
                // Esc键触发智能返回
                if (e.key === 'Escape') {
                    e.preventDefault();
                    smartGoBack();
                }
                // Ctrl+S 手动保存草稿
                if (e.ctrlKey && e.key === 's') {
                    e.preventDefault();
                    autoSavePostDraft();
                    // 显示保存提示（使用对话框形式）
                    const message = postId === 'new' ? '草稿已保存' : '编辑草稿已保存';
                    showSaveDialog(message);
                }
            });

            function insertMarkdown(before, after) {
                const start = textarea.selectionStart;
                const end = textarea.selectionEnd;
                const selectedText = textarea.value.substring(start, end);
                const newText = before + selectedText + after;

                textarea.value = textarea.value.substring(0, start) + newText + textarea.value.substring(end);
                textarea.selectionStart = start + before.length;
                textarea.selectionEnd = start + before.length + selectedText.length;
                textarea.focus();

                // 触发 input 事件以更新行号、字数统计等
                textarea.dispatchEvent(new Event('input', { bubbles: true }));
                updateSelectionCount(); // 更新选中统计
            }

            function insertText(text) {
                const start = textarea.selectionStart;
                textarea.value = textarea.value.substring(0, start) + text + textarea.value.substring(start);
                textarea.selectionStart = textarea.selectionEnd = start + text.length;
                textarea.focus();

                // 触发 input 事件以更新行号、字数统计等
                textarea.dispatchEvent(new Event('input', { bubbles: true }));
                updateSelectionCount(); // 更新选中统计
            }

            // 设置默认发布时间（如果是新帖子且没有提供时间）
            if (createdAtInput && !createdAtInput.value) {
                const now = new Date();
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                
                createdAtInput.value = `${year}-${month}-${day} ${hours}:${minutes}`;
            }
            
            // 表单提交处理（区分保存和发布操作）
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const submitter = e.submitter;
                    
                    // 检查是否是保存操作
                    if (submitter && submitter.value === 'save') {
                        // 保存操作：使用 AJAX，无刷新
                        e.preventDefault();
                        handleSaveWithoutRefresh(form);
                    } else {
                        // 发布操作：使用传统提交，删除草稿
                        removePostDraft(postId);
                    }
                });
            }
            
            // AJAX 保存处理函数
            function handleSaveWithoutRefresh(form) {
                const formData = new FormData(form);
                
                // 显示保存中状态
                const saveButton = document.querySelector('button[value="save"]');
                const originalText = saveButton.textContent;
                saveButton.disabled = true;
                saveButton.textContent = '保存中...';
                
                fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('保存失败');
                    }
                    return response.json();
                })
                .then(data => {
                    if (postId === 'new') {
                        // 新建模式：跳转到编辑页面
                        window.location.href = `/post/${data.id}/edit?saved=1`;
                    } else {
                        // 编辑模式：显示提示并更新状态
                        showSaveDialog('保存成功！可继续编辑或点击发布。');
                        
                        // 更新原始值，重置变更检测状态
                        if (titleInput) titleInput.setAttribute('data-original-value', titleInput.value);
                        if (categoryInput) categoryInput.setAttribute('data-original-value', categoryInput.value);
                        if (textarea) textarea.setAttribute('data-original-value', textarea.value);
                        if (createdAtInput) createdAtInput.setAttribute('data-original-value', createdAtInput.value);
                    }
                })
                .catch(error => {
                    console.error('保存失败:', error);
                    showSaveDialog('保存失败，请重试。');
                })
                .finally(() => {
                    // 恢复按钮状态
                    saveButton.disabled = false;
                    saveButton.textContent = originalText;
                });
            }
        }
        
        // 注意：已移除页面离开前提醒，因为有自动草稿保存功能
        
        // 清理过期草稿
        function cleanupExpiredPostDrafts() {
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith('haobbs_draft_post_')) {
                    try {
                        const draftData = JSON.parse(localStorage.getItem(key));
                        if (draftData && draftData.expires && Date.now() > draftData.expires) {
                            localStorage.removeItem(key);
                        }
                    } catch (e) {
                        localStorage.removeItem(key);
                    }
                }
            });
        }
        
        cleanupExpiredPostDrafts();
        
        // 初始化字段折叠功能
        initFieldsToggle();

        // 初始化行号编辑器功能
        initLineNumberEditor();
    });
</script>

<!-- 表单容器 -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 md:p-8 form-container">
    <form action="{{ form_action }}" method="post">
        <!-- 字段折叠切换按钮 -->
        <div id="fields-toggle" class="fields-toggle">
            <svg id="toggle-icon" class="fields-toggle-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
            <span id="toggle-text" class="fields-toggle-text">隐藏表单字段</span>
        </div>
        
        <!-- 可折叠的表单字段容器 -->
        <div id="form-fields-container" class="form-fields-container expanded">
            <!-- 发布时间、分类、标题水平布局 -->
            <div class="form-row">
                <!-- 自定义发布时间 - 自适应宽度 -->
                <div class="form-group flex-none">
                    <label class="form-label">发布时间</label>
                    <input type="text" name="created_at" id="created_at" 
                           class="form-input-datetime" placeholder="YYYY-MM-DD HH:MM"
                           value="{{ created_at_value }}">
                </div>

                <!-- 分类 - 自适应宽度 -->
                <div class="form-group flex-none">
                    <label class="form-label">帖子分类</label>
                    <input type="text" name="category" id="category" required 
                           class="form-input-category" placeholder="请输入分类名称" value="{{ category_value }}">
                </div>

                <!-- 标题 - 占据剩余宽度 -->
                <div class="form-group flex-1">
                    <label class="form-label">帖子标题</label>
                    <input type="text" name="title" id="title" required 
                           class="form-input" placeholder="请输入帖子标题" value="{{ title_value }}">
                </div>
            </div>
        </div>

        <!-- 内容 -->
        <div class="form-group content-group">
            <div class="flex-between-center mb-2">
                <label class="form-label">帖子内容</label>
                <div class="flex-center">
                    <span id="selection-count" class="selection-count empty">选中: 0 字</span>
                    <span id="character-count" class="character-count">0 字</span>
                </div>
            </div>
            <div class="editor-container">
                <div class="line-numbers" id="line-numbers">1</div>
                <div class="editor-wrapper">
                    <textarea name="content" id="content" required
                              class="form-textarea-with-lines"
                              placeholder="支持 Markdown 语法，请输入帖子内容...">{{ content_value }}</textarea>
                </div>
            </div>
        </div>

        <!-- 按钮组 -->
        <div class="btn-group">
            <button type="button" onclick="smartGoBack()" class="btn-secondary">返回</button>
            {% if submit_button_text == '保存修改' %}
                <!-- 编辑模式：显示保存和发布两个按钮 -->
                <button type="submit" name="action_type" value="save" class="btn-primary">保存</button>
                <button type="submit" name="action_type" value="publish" class="btn-success">发布</button>
            {% else %}
                <!-- 新建模式：显示保存草稿和发布帖子两个按钮 -->
                <button type="submit" name="action_type" value="save" class="btn-primary">保存草稿</button>
                <button type="submit" name="action_type" value="publish" class="btn-success">{{ submit_button_text }}</button>
            {% endif %}
        </div>
    </form>
</div>
